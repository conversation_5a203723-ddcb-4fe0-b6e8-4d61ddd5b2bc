%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 468a46d0ae32c3544b7d98094e6448a9, type: 3}
  m_Name: AddressableAssetSettings
  m_EditorClassIdentifier: 
  m_DefaultGroup: ec09b60180af14e47b4e7cbf82cca15d
  m_currentHash:
    serializedVersion: 2
    Hash: 84f1ce27d1cfd8b1f444c434918efcff
  m_OptimizeCatalogSize: 0
  m_BuildRemoteCatalog: 0
  m_CatalogRequestsTimeout: 0
  m_DisableCatalogUpdateOnStart: 0
  m_InternalIdNamingMode: 0
  m_InternalBundleIdMode: 1
  m_AssetLoadMode: 0
  m_BundledAssetProviderType:
    m_AssemblyName: 
    m_ClassName: 
  m_AssetBundleProviderType:
    m_AssemblyName: 
    m_ClassName: 
  m_IgnoreUnsupportedFilesInBuild: 0
  m_UniqueBundleIds: 0
  m_EnableJsonCatalog: 0
  m_NonRecursiveBuilding: 1
  m_CCDEnabled: 0
  m_maxConcurrentWebRequests: 3
  m_UseUWRForLocalBundles: 0
  m_BundleTimeout: 0
  m_BundleRetryCount: 0
  m_BundleRedirectLimit: -1
  m_SharedBundleSettings: 0
  m_SharedBundleSettingsCustomGroupIndex: 0
  m_ContiguousBundles: 1
  m_StripUnityVersionFromBundleBuild: 0
  m_DisableVisibleSubAssetRepresentations: 0
  m_BuiltInBundleNaming: 0
  mBuiltInBundleCustomNaming: 
  m_MonoScriptBundleNaming: 0
  m_CheckForContentUpdateRestrictionsOption: 0
  m_MonoScriptBundleCustomNaming: 
  m_RemoteCatalogBuildPath:
    m_Id: 50da580fe81df4c918eb2d3cdc8f5636
  m_RemoteCatalogLoadPath:
    m_Id: a52f6e3cb4372427480e78c3f195ccd1
  m_ContentStateBuildPathProfileVariableName: <default settings path>
  m_CustomContentStateBuildPath: 
  m_ContentStateBuildPath: 
  m_BuildAddressablesWithPlayerBuild: 0
  m_overridePlayerVersion: '[UnityEditor.PlayerSettings.bundleVersion]'
  m_GroupAssets:
  - {fileID: 11400000, guid: d046b6ac7762f4611bed3c2b677c6d3e, type: 2}
  - {fileID: 11400000, guid: e5fcfce0df21d481ab70eebd115e724c, type: 2}
  - {fileID: 11400000, guid: 47d14fd3b4d86433bbaa00faaae572ce, type: 2}
  - {fileID: 11400000, guid: f8c550485465e48df889ea88efd2953e, type: 2}
  - {fileID: 11400000, guid: c9f71145796a045c9bf8fb6bba1ce107, type: 2}
  - {fileID: 11400000, guid: 87979e5ba8e834d95bae46c91a1547aa, type: 2}
  - {fileID: 11400000, guid: c2d6fcae763c84c81838f02b99696c74, type: 2}
  m_BuildSettings:
    m_LogResourceManagerExceptions: 1
    m_BundleBuildPath: Temp/com.unity.addressables/AssetBundles
  m_ProfileSettings:
    m_Profiles:
    - m_InheritedParent: 
      m_Id: ae8b3512199104faa9ffc9636c576b4a
      m_ProfileName: Default
      m_Values:
      - m_Id: 01d274410b6294f9fb453da17bff457c
        m_Value: '[UnityEngine.AddressableAssets.Addressables.BuildPath]/[BuildTarget]'
      - m_Id: 47a55b5a95aab40d3aa72dce99c5e035
        m_Value: '[UnityEditor.EditorUserBuildSettings.activeBuildTarget]'
      - m_Id: 50da580fe81df4c918eb2d3cdc8f5636
        m_Value: 'ServerData/[BuildTarget]'
      - m_Id: 5423eae06e37440c38870f812f45cfd1
        m_Value: '{UnityEngine.AddressableAssets.Addressables.RuntimePath}/[BuildTarget]'
      - m_Id: a52f6e3cb4372427480e78c3f195ccd1
        m_Value: <undefined>
    m_ProfileEntryNames:
    - m_Id: 01d274410b6294f9fb453da17bff457c
      m_Name: Local.BuildPath
      m_InlineUsage: 0
    - m_Id: 47a55b5a95aab40d3aa72dce99c5e035
      m_Name: BuildTarget
      m_InlineUsage: 0
    - m_Id: 50da580fe81df4c918eb2d3cdc8f5636
      m_Name: Remote.BuildPath
      m_InlineUsage: 0
    - m_Id: 5423eae06e37440c38870f812f45cfd1
      m_Name: Local.LoadPath
      m_InlineUsage: 0
    - m_Id: a52f6e3cb4372427480e78c3f195ccd1
      m_Name: Remote.LoadPath
      m_InlineUsage: 0
    m_ProfileVersion: 1
  m_LabelTable:
    m_LabelNames:
    - default
    - Locale
    - Locale-zh-CN
    - Locale-en-US
  m_SchemaTemplates: []
  m_GroupTemplateObjects:
  - {fileID: 11400000, guid: 8482034f7dc274262a7aa66abc63cfc8, type: 2}
  m_InitializationObjects: []
  m_CertificateHandlerType:
    m_AssemblyName: 
    m_ClassName: 
  m_ActivePlayerDataBuilderIndex: 2
  m_DataBuilders:
  - {fileID: 11400000, guid: 301b1d77a5a2446269211dadf81b8c6c, type: 2}
  - {fileID: 11400000, guid: 061856502300a4bb18943e93b8ff3c02, type: 2}
  - {fileID: 11400000, guid: 88b372b66626144ed885ca75fdcd3641, type: 2}
  m_ActiveProfileId: ae8b3512199104faa9ffc9636c576b4a
